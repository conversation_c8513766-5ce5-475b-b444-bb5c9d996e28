# Jenkins CI/CD Setup cho Robot Framework

## 🐳 **Docker-based Pipeline**

**Jenkinsfile đã đượ<PERSON> chuyển sang Docker:**
- ✅ **Sử dụng Docker containers** thay vì native setup
- ✅ **Consistent environment** across all builds
- ✅ **No dependency conflicts** với <PERSON> host
- ✅ **Easy cleanup** và resource management
- ✅ **Scalable và portable** deployment

**Các lỗi đã fix:**

1. **Sudo permission denied** → Loại bỏ sudo commands
2. **Robot Framework plugin missing** → Thay thế hoàn toàn bằng publishHTML (không cần plugin)
3. **Python 3.10 not found** → Multi-tier installation với fallback
4. **python3-venv not available** → Fallback với --without-pip + manual pip
5. **Stages skipped due to failure** → Robust error handling
6. **Exit code 127** → Fix Python detection logic (test execution, not just command existence)
7. **SessionNotCreatedException** → Sử dụng Chrome có sẵn + stable ChromeDriver versions

## 🚀 **Pipeline tự động:**

1. **Kiểm tra/cài Python 3.10** - tự động skip nếu có sẵn (~30 giây)
2. Setup virtual environment với Python 3.10
3. Cài đặt dependencies từ requirements.txt
4. Setup biến môi trường Robot Framework
5. Kiểm tra browser dependencies
6. Chạy tests qua `run_tests.py`
7. Archive results và gửi email

## 📋 **Pipeline Stages (Docker-based):**

1. **Checkout** → Git checkout source code
2. **Build Docker Image** → Build Robot Framework image với Python 3.10 + Chrome + ChromeDriver
3. **Run Tests** → Chạy tests trong Docker container với volume mounts
4. **Archive & Email** → Publish HTML reports + gửi email notifications + cleanup Docker resources

## 🐳 **Docker Requirements**

**Jenkins server cần có Docker:**
1. **Docker Engine** installed và running
2. **Jenkins user** có quyền access Docker socket
3. **Docker Pipeline Plugin** installed trong Jenkins

**Setup Docker cho Jenkins:**
```bash
# Add jenkins user to docker group
sudo usermod -aG docker jenkins

# Restart Jenkins service
sudo systemctl restart jenkins

# Test Docker access
sudo -u jenkins docker ps
```

---

## 🔧 **Cài đặt Jenkins Plugins**

**Bắt buộc** (plugins cơ bản):

1. **Docker Pipeline Plugin** → Docker integration trong Pipeline
2. **Pipeline Plugin** (thường có sẵn)
3. **Git Plugin** (thường có sẵn)
4. **Email Extension Plugin** (thường có sẵn)
5. **HTML Publisher Plugin** (thường có sẵn) - **Thay thế Robot Framework Plugin**

**Không cần:** Robot Framework Plugin (đã thay thế bằng HTML Publisher)
5. **Git Plugin**

**Cách cài:** Jenkins → Manage Jenkins → Manage Plugins → Available → Tìm và cài đặt

## ⚙️ **Setup Jenkins**

### 1. Cấu hình Email
**Manage Jenkins → Configure System → Extended E-mail Notification:**
```
SMTP Server: smtp.gmail.com
SMTP Port: 587
Username: <EMAIL>
Password: your-app-password
Use SSL: ✓
```

### 2. Tạo Pipeline Job
1. **New Item** → **Pipeline**
2. **Pipeline** section:
   - Definition: **Pipeline script from SCM**
   - SCM: **Git**
   - Repository URL: `https://github.com/your-repo/Robot_Framework_CLO.git`
   - Script Path: **Jenkinsfile**

## 🔧 **Troubleshooting**

### Lỗi thường gặp:

**1. "No such DSL method 'robot'"**
→ Cài đặt Robot Framework Plugin

**2. "Chrome not found"**
→ Pipeline sẽ tự động download ChromeDriver

**3. "python3-venv not available"**
→ Pipeline sẽ tự động fallback với --without-pip + manual pip

**4. "DSL method 'robot' not found"**
→ Thay thế hoàn toàn bằng publishHTML (không cần Robot plugin)

**5. "Stages skipped due to earlier failure"**
→ Robust error handling ngăn cascade failures

**6. "Exit code 127"**
→ Better Python detection và multiple fallbacks

**7. "SessionNotCreatedException"**
→ Sử dụng Chrome có sẵn + stable ChromeDriver versions (không cài mới)

**4. Email không gửi được**
→ Kiểm tra cấu hình SMTP

## ⚡ **Tối ưu tốc độ:**
- **Có Python 3.10 sẵn**: ~30 giây (auto skip)
- **Cần cài mới**: ~3-5 phút (pre-compiled binary)
- **System package**: ~1 phút (apt install)

## 📧 **Kết quả**

Pipeline tự động:
- ✅ Archive test results
- ✅ Publish HTML reports
- ✅ Gửi email notifications (Success/Failure)
- ✅ Cleanup processes

## 📞 **Hỗ trợ**

Email: <EMAIL>
