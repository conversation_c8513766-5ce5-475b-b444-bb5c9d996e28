pipeline {
    agent any

    environment {
        DOCKER_IMAGE = "robot-framework-tests"
        DOCKER_TAG = "${BUILD_NUMBER}"
        DOCKER_LATEST = "latest"
        RESULTS_DIR = "results"
    }

    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out code from GitHub...'
                checkout scm
            }
        }

        stage('Build Docker Image') {
            steps {
                echo 'Building Robot Framework Docker image...'
                script {
                    sh """
                        echo "Current directory: \$(pwd)"
                        echo "Listing files:"
                        ls -la

                        cd deploy
                        echo "Building Docker image..."
                        docker build -f Dockerfile -t ${DOCKER_IMAGE}:${DOCKER_TAG} ..
                        docker tag ${DOCKER_IMAGE}:${DOCKER_TAG} ${DOCKER_IMAGE}:${DOCKER_LATEST}

                        echo "Docker image built: ${DOCKER_IMAGE}:${DOCKER_TAG}"
                    """
                }
            }
        }

        stage('Test Image') {
            steps {
                echo 'Testing Docker image...'
                script {
                    sh """
                        echo "Testing if image exists..."
                        docker images | grep ${DOCKER_IMAGE}

                        echo "Current build tag: ${DOCKER_TAG}"
                        if docker images --format "table" | grep -q "${DOCKER_IMAGE}"; then
                            echo "Image found!"
                        else
                            echo "Image not found!"
                            exit 1
                        fi

                        echo "Testing basic container run..."
                        docker run --rm ${DOCKER_IMAGE}:${DOCKER_TAG} python --version

                        echo "Testing Robot Framework installation..."
                        docker run --rm ${DOCKER_IMAGE}:${DOCKER_TAG} python -c "import robot; print('Robot Framework OK')"

                        echo "Testing Chrome installation..."
                        docker run --rm ${DOCKER_IMAGE}:${DOCKER_TAG} google-chrome --version

                        echo "Testing ChromeDriver installation..."
                        docker run --rm ${DOCKER_IMAGE}:${DOCKER_TAG} chromedriver --version
                    """
                }
            }
        }

        stage('Run Robot Framework Tests') {
            steps {
                echo 'Running Robot Framework tests...'
                script {
                    sh """
                        echo "Creating results directory..."
                        mkdir -p ${WORKSPACE}/${RESULTS_DIR}

                        echo "Starting Robot Framework test container..."
                        docker run -d --name robot-tests-${BUILD_NUMBER} \
                            -v ${WORKSPACE}/${RESULTS_DIR}:/app/results \
                            -e DISPLAY=:99 \
                            -e PYTHONPATH=/app \
                            ${DOCKER_IMAGE}:${DOCKER_TAG}

                        echo "Waiting for tests to start..."
                        sleep 5

                        echo "Checking container status..."
                        docker ps -a | grep robot-tests-${BUILD_NUMBER} || echo "Container not found"

                        echo "Getting container logs..."
                        docker logs robot-tests-${BUILD_NUMBER} 2>&1 || echo "No logs available"

                        echo "Waiting for tests to complete..."
                        docker wait robot-tests-${BUILD_NUMBER} || true

                        echo "Final container logs:"
                        docker logs robot-tests-${BUILD_NUMBER} 2>&1

                        echo "Checking test results..."
                        if [ -f "${WORKSPACE}/${RESULTS_DIR}/output.xml" ]; then
                            echo "Test results found!"
                            ls -la ${WORKSPACE}/${RESULTS_DIR}/
                        else
                            echo "No test results found!"
                            echo "Container status:"
                            docker ps -a | grep robot-tests-${BUILD_NUMBER}
                        fi

                        echo "Cleaning up test container..."
                        docker rm robot-tests-${BUILD_NUMBER} || true
                    """
                }
            }
        }

        stage('Cleanup') {
            steps {
                echo 'Cleaning up old Docker images...'
                script {
                    sh """
                        echo "Current Docker images:"
                        docker images ${DOCKER_IMAGE}

                        echo "Cleaning up old images (keeping latest 3 versions)..."
                        docker images ${DOCKER_IMAGE} --format "table" | grep -E '[0-9]+' | tail -n +4 | while read line; do
                            tag=\$(echo \$line | awk '{print \$2}')
                            echo "Removing ${DOCKER_IMAGE}:\$tag"
                            docker rmi ${DOCKER_IMAGE}:\$tag || true
                        done

                        echo "Cleaning up dangling images..."
                        docker image prune -f || true

                        echo "Final Docker images:"
                        docker images ${DOCKER_IMAGE}
                    """
                }
            }
        }
    }


    post {
        always {
            echo 'Pipeline completed!'

            // Archive test results
            archiveArtifacts artifacts: "${RESULTS_DIR}/**/*", allowEmptyArchive: true

            // Publish HTML reports
            script {
                if (fileExists("${RESULTS_DIR}/output.xml")) {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: "${RESULTS_DIR}",
                        reportFiles: 'report.html',
                        reportName: 'Robot Framework Report'
                    ])

                    if (fileExists("${RESULTS_DIR}/log.html")) {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: "${RESULTS_DIR}",
                            reportFiles: 'log.html',
                            reportName: 'Robot Framework Log'
                        ])
                    }
                }
            }

            // Dọn dẹp workspace nếu cần
            cleanWs()
        }

        success {
            echo 'Robot Framework tests successful!'
        }
        failure {
            echo 'Pipeline failed!'
        }
    }
}
