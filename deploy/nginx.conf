server {
    listen 80;
    server_name localhost;
    
    root /usr/share/nginx/html;
    index report.html log.html index.html;
    
    # Enable directory listing
    autoindex on;
    autoindex_exact_size off;
    autoindex_localtime on;
    
    # Serve static files
    location / {
        try_files $uri $uri/ =404;
    }
    
    # Specific handling for Robot Framework files
    location ~* \.(html|xml)$ {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
